import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get project root directory (where config.py is located)
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# App Configuration
APP_NAME = os.getenv('APP_NAME', 'MIT_CVD_APP')
DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'

# Directory Configuration
DATA_DIR = os.path.join(PROJECT_ROOT, os.getenv('DATA_DIR', 'data'))
REPORTS_DIR = os.path.join(PROJECT_ROOT, os.getenv('REPORTS_DIR', 'reports'))
TEMP_DIR = os.path.join(PROJECT_ROOT, os.getenv('TEMP_DIR', 'temp'))
BACKEND_DIR = os.path.join(PROJECT_ROOT, 'backend')
FRONT_DIR = os.path.join(PROJECT_ROOT, 'front')

# Ensure directories exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(REPORTS_DIR, exist_ok=True)
os.makedirs(TEMP_DIR, exist_ok=True)

# File paths
USERS_DB_PATH = os.path.join(DATA_DIR, 'users.json')
SESSIONS_DB_PATH = os.path.join(DATA_DIR, 'sessions.json')
STREAKS_DB_PATH = os.path.join(DATA_DIR, 'streaks.json')
FOOD_HISTORY_PATH = os.path.join(DATA_DIR, 'food_analysis_history.json')

# Test file path
TEST_FILE_PATH = os.path.join(PROJECT_ROOT, 'teste.txt')

# CVD Risk Calculation Constants
CVD_RISK_WEIGHTS = {
    'age': 0.2,
    'family_history': 0.25,
    'smoking': 0.2,
    'exercise': 0.15,
    'diet_score': 0.1,
    'stress_level': 0.1
}

# Gamification Settings
STREAK_BONUS_MULTIPLIER = 1.1
MAX_DAILY_POINTS = 100
