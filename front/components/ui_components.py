"""
UI Components for MIT CVD App
Reusable Streamlit components for the frontend
"""
import streamlit as st
from typing import Dict, Any, List, Optional

def show_metric_card(title: str, value: str, help_text: str = None, color: str = "blue"):
    """Display a metric card with optional help text"""
    with st.container():
        if color == "red":
            st.error(f"**{title}:** {value}")
        elif color == "green":
            st.success(f"**{title}:** {value}")
        elif color == "orange":
            st.warning(f"**{title}:** {value}")
        else:
            st.info(f"**{title}:** {value}")
        
        if help_text:
            st.caption(help_text)

def show_risk_level_badge(risk_level: str, score: float):
    """Display a risk level badge with appropriate styling"""
    if risk_level == "Low":
        st.success(f"🟢 **{risk_level} Risk** ({score:.1%})")
    elif risk_level == "Moderate":
        st.warning(f"🟡 **{risk_level} Risk** ({score:.1%})")
    elif risk_level == "High":
        st.error(f"🟠 **{risk_level} Risk** ({score:.1%})")
    elif risk_level == "Very High":
        st.error(f"🔴 **{risk_level} Risk** ({score:.1%})")
    else:
        st.info(f"❓ **Unknown Risk** ({score:.1%})")

def show_family_history_summary(family_history: Dict[str, Any]) -> None:
    """Display a summary of family history"""
    if not family_history:
        st.write("No family history recorded")
        return
    
    conditions = []
    for condition, present in family_history.items():
        if present and not condition.endswith('_early_onset') and not condition.endswith('_multiple'):
            # Format condition name
            formatted_condition = condition.replace('_', ' ').title()
            
            # Check for early onset or multiple affected
            early_onset = family_history.get(f'{condition}_early_onset', False)
            multiple = family_history.get(f'{condition}_multiple', False)
            
            details = []
            if early_onset:
                details.append("early onset")
            if multiple:
                details.append("multiple affected")
            
            if details:
                formatted_condition += f" ({', '.join(details)})"
            
            conditions.append(formatted_condition)
    
    if conditions:
        for condition in conditions:
            st.write(f"• {condition}")
    else:
        st.write("No family history recorded")

def show_lifestyle_summary(lifestyle: Dict[str, Any]) -> None:
    """Display a summary of lifestyle factors"""
    if not lifestyle:
        st.write("No lifestyle data recorded")
        return
    
    col1, col2 = st.columns(2)
    
    with col1:
        exercise_freq = lifestyle.get('exercise_frequency', 0)
        if exercise_freq >= 5:
            st.success(f"🏃‍♂️ Very Active ({exercise_freq}x/week)")
        elif exercise_freq >= 3:
            st.info(f"🚶‍♂️ Active ({exercise_freq}x/week)")
        elif exercise_freq >= 1:
            st.warning(f"🚶 Lightly Active ({exercise_freq}x/week)")
        else:
            st.error("😴 Sedentary (0x/week)")
        
        smoking = lifestyle.get('smoking', False)
        if smoking:
            st.error("🚬 Current Smoker")
        else:
            st.success("🚭 Non-smoker")
    
    with col2:
        sleep_hours = lifestyle.get('sleep_hours', 8)
        if 7 <= sleep_hours <= 8:
            st.success(f"😴 Good Sleep ({sleep_hours}h)")
        elif sleep_hours < 6:
            st.error(f"😰 Poor Sleep ({sleep_hours}h)")
        else:
            st.warning(f"😴 Sleep ({sleep_hours}h)")
        
        stress_level = lifestyle.get('stress_level', 5)
        if stress_level <= 3:
            st.success(f"😌 Low Stress ({stress_level}/10)")
        elif stress_level <= 6:
            st.info(f"😐 Moderate Stress ({stress_level}/10)")
        else:
            st.error(f"😰 High Stress ({stress_level}/10)")

def show_medical_conditions_summary(conditions: List[str], medications: List[str]) -> None:
    """Display a summary of medical conditions and medications"""
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Medical Conditions:**")
        if conditions and conditions != ["None"]:
            for condition in conditions:
                if condition != "None":
                    st.write(f"• {condition}")
        else:
            st.write("None reported")
    
    with col2:
        st.markdown("**Current Medications:**")
        if medications:
            for medication in medications:
                st.write(f"• {medication}")
        else:
            st.write("None reported")

def show_section_header(title: str, icon: str = "📋", help_text: str = None):
    """Display a consistent section header"""
    if help_text:
        st.subheader(f"{icon} {title}", help=help_text)
    else:
        st.subheader(f"{icon} {title}")

def show_update_success(message: str = "Information updated successfully!"):
    """Display a success message for updates"""
    st.success(f"✅ {message}")

def show_update_error(message: str = "Failed to update information"):
    """Display an error message for updates"""
    st.error(f"❌ {message}")

def show_info_box(title: str, content: str, type: str = "info"):
    """Display an information box"""
    if type == "warning":
        st.warning(f"**{title}**: {content}")
    elif type == "error":
        st.error(f"**{title}**: {content}")
    elif type == "success":
        st.success(f"**{title}**: {content}")
    else:
        st.info(f"**{title}**: {content}")

def create_two_column_form(left_items: List[tuple], right_items: List[tuple]):
    """Create a two-column form layout
    
    Args:
        left_items: List of tuples (component_type, label, value, kwargs)
        right_items: List of tuples (component_type, label, value, kwargs)
    """
    col1, col2 = st.columns(2)
    
    results = {}
    
    with col1:
        for item in left_items:
            component_type, label, value, kwargs = item
            if component_type == "text_input":
                results[label] = st.text_input(label, value=value, **kwargs)
            elif component_type == "number_input":
                results[label] = st.number_input(label, value=value, **kwargs)
            elif component_type == "selectbox":
                results[label] = st.selectbox(label, value=value, **kwargs)
            elif component_type == "checkbox":
                results[label] = st.checkbox(label, value=value, **kwargs)
            elif component_type == "slider":
                results[label] = st.slider(label, value=value, **kwargs)
    
    with col2:
        for item in right_items:
            component_type, label, value, kwargs = item
            if component_type == "text_input":
                results[label] = st.text_input(label, value=value, **kwargs)
            elif component_type == "number_input":
                results[label] = st.number_input(label, value=value, **kwargs)
            elif component_type == "selectbox":
                results[label] = st.selectbox(label, value=value, **kwargs)
            elif component_type == "checkbox":
                results[label] = st.checkbox(label, value=value, **kwargs)
            elif component_type == "slider":
                results[label] = st.slider(label, value=value, **kwargs)
    
    return results

def show_confirmation_dialog(message: str, key: str) -> bool:
    """Show a confirmation dialog"""
    st.warning(f"⚠️ {message}")
    col1, col2 = st.columns(2)
    
    with col1:
        confirm = st.button("✅ Confirm", key=f"{key}_confirm")
    with col2:
        cancel = st.button("❌ Cancel", key=f"{key}_cancel")
    
    return confirm

def show_progress_indicator(current_step: int, total_steps: int, step_names: List[str] = None):
    """Show a progress indicator"""
    progress = current_step / total_steps
    st.progress(progress)
    
    if step_names and len(step_names) >= total_steps:
        st.write(f"Step {current_step}/{total_steps}: {step_names[current_step-1]}")
    else:
        st.write(f"Step {current_step}/{total_steps}")

class FormBuilder:
    """Helper class to build forms with consistent styling"""
    
    def __init__(self, title: str):
        self.title = title
        self.sections = []
    
    def add_section(self, section_title: str, fields: List[Dict[str, Any]]):
        """Add a section to the form"""
        self.sections.append({
            'title': section_title,
            'fields': fields
        })
    
    def render(self) -> Dict[str, Any]:
        """Render the form and return the values"""
        st.subheader(self.title)
        
        values = {}
        
        for section in self.sections:
            st.markdown(f"**{section['title']}**")
            
            for field in section['fields']:
                field_type = field.get('type', 'text_input')
                label = field['label']
                default_value = field.get('value', '')
                kwargs = field.get('kwargs', {})
                
                if field_type == 'text_input':
                    values[label] = st.text_input(label, value=default_value, **kwargs)
                elif field_type == 'number_input':
                    values[label] = st.number_input(label, value=default_value, **kwargs)
                elif field_type == 'selectbox':
                    values[label] = st.selectbox(label, **kwargs)
                elif field_type == 'checkbox':
                    values[label] = st.checkbox(label, value=default_value, **kwargs)
                elif field_type == 'slider':
                    values[label] = st.slider(label, value=default_value, **kwargs)
                elif field_type == 'multiselect':
                    values[label] = st.multiselect(label, **kwargs)
                elif field_type == 'text_area':
                    values[label] = st.text_area(label, value=default_value, **kwargs)
            
            st.markdown("---")
        
        return values
