import streamlit as st
import warnings

# Suppress deprecation warnings (optional - uncomment if needed)
# warnings.filterwarnings("ignore", category=DeprecationWarning)

from pages import daily_food, login, tasks, assistant_voice, profile

# Constants
PAGE_HOME = "Home"
PAGE_DAILY_FOOD = "Daily Food"
PAGE_TASK = "Task"
PAGE_ASSISTANT = "Assistant"
PAGE_PROFILE = "Profile"

PAGES = [PAGE_HOME, PAGE_DAILY_FOOD, PAGE_TASK, PAGE_ASSISTANT, PAGE_PROFILE]

# Configure page
st.set_page_config(
    page_title="AIDCTIVE App",
    page_icon="❤️",
    layout="wide"
)

# Initialize session state
if 'logged_in' not in st.session_state:
    st.session_state.logged_in = False
if 'user' not in st.session_state:
    st.session_state.user = None

# Check if user is logged in
if not st.session_state.logged_in:
    # Show login page if not logged in
    login.main()
    # Stop execution here to prevent showing the main app
    st.stop()

# Show main app with sidebar if logged in
st.sidebar.write(f"👋 Hello, **{st.session_state.user['name']}**")

if st.sidebar.button("🚪 Logout"):
    st.session_state.logged_in = False
    st.session_state.user = None
    if 'redirect_to' in st.session_state:
        del st.session_state.redirect_to
    if 'current_page' in st.session_state:
        del st.session_state.current_page
    st.rerun()

st.sidebar.markdown("---")

# Centralized page navigation state
if 'current_page' not in st.session_state:
    st.session_state.current_page = PAGE_HOME

def set_page(page):
    st.session_state.current_page = page

# Handle redirects from login or quick actions
if 'redirect_to' in st.session_state:
    st.session_state.current_page = st.session_state.redirect_to
    del st.session_state.redirect_to

side_bar = st.sidebar.selectbox(
    "Select a page",
    options=PAGES,
    index=PAGES.index(st.session_state.current_page) if st.session_state.current_page in PAGES else 0,
    on_change=lambda: set_page(st.session_state['new_page']),
    key='new_page'
)

# Update current page based on sidebar selection
st.session_state.current_page = side_bar

# Main router based on current page
if st.session_state.current_page == PAGE_HOME:
    st.title("🏠 Dashboard - MIT CVD App")
    
    # Welcome message
    st.markdown(f"## 👋 Welcome, **{st.session_state.user['name']}**!")
    st.markdown("---")
    
    # Patient Information Section
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("### 📝 **Personal Information**")
        with st.container():
            st.write(f"**👤 Name:** {st.session_state.user['name']}")
            st.write(f"**📧 Email:** {st.session_state.user['email']}")
            st.write(f"**🔑 Username:** {st.session_state.user['username']}")
            st.write(f"**📅 Member since:** {st.session_state.user['created_at'][:10]}")
            if st.session_state.user.get('age'):
                st.write(f"**🎂 Age:** {st.session_state.user['age']} years")
    
    with col2:
        st.markdown("### 🏃‍♂️ **Lifestyle Overview**")
        with st.container():
            lifestyle = st.session_state.user.get('profile', {}).get('lifestyle', {})
            st.write(f"**💪 Exercise:** {lifestyle.get('exercise_frequency', 0)} times/week")
            st.write(f"**💤 Sleep:** {lifestyle.get('sleep_hours', 8)} hours/night")
            st.write(f"**🚬 Smoking:** {'Yes' if lifestyle.get('smoking', False) else 'No'}")
            st.write(f"**😰 Stress Level:** {lifestyle.get('stress_level', 5)}/10")
    
    with col3:
        st.markdown("### 🧬 **Family History**")
        with st.container():
            family_history = st.session_state.user.get("profile", {}).get("family_history", {})
            heart_attack = "✅ Yes" if family_history.get('heart_attack', False) else "❌ No"
            high_bp = "✅ Yes" if family_history.get('high_blood_pressure', False) else "❌ No"
            st.write(f"**💔 Family heart attack:** {heart_attack}")
            st.write(f"**🩸 Family hypertension:** {high_bp}")
    
    st.markdown("---")
    
    # CVD Scores Section
    cvd_scores = st.session_state.user.get('cvd_scores', [])
    
    if cvd_scores:
        st.markdown("### ❤️ **Cardiovascular Risk Assessments**")
        
        # Latest score summary
        latest_score = cvd_scores[-1]
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            score_value = latest_score['score']
            if score_value > 0.7:
                score_color = "red"
            elif score_value > 0.4:
                score_color = "orange"
            else:
                score_color = "green"
            
            st.metric(
                "🎯 Current CVD Score", 
                f"{score_value:.3f}",
                help=f"Risk Level: {latest_score['risk_level']}"
            )
        
        with col2:
            factors = latest_score.get('factors', {})
            age_risk = factors.get('age_risk', 0)
            st.metric("📅 Age Risk", f"{age_risk:.2f}")
        
        with col3:
            family_risk = factors.get('family_risk', 0)
            st.metric("🧬 Family Risk", f"{family_risk:.2f}")
        
        with col4:
            lifestyle_risk = factors.get('lifestyle_risk', 0)
            st.metric("🏃 Lifestyle Risk", f"{lifestyle_risk:.2f}")
        
        # Historical trend
        if len(cvd_scores) > 1:
            st.markdown("#### 📈 **Historical Trend**")
            
            import pandas as pd
            import numpy as np
            
            # Create dataframe for chart
            dates = [score['calculated_at'][:10] for score in cvd_scores]
            scores = [score['score'] for score in cvd_scores]
            
            chart_data = pd.DataFrame({
                'Date': dates,
                'CVD Score': scores
            })
            
            st.line_chart(chart_data.set_index('Date'))
    
    else:
        st.markdown("### ❤️ **Cardiovascular Risk Assessments**")
        st.info("🔍 You haven't performed any cardiovascular risk assessments yet.")
        st.write("💡 **Tip:** Use the app pages to perform your first assessment!")
    
    # Quick actions
    st.markdown("---")
    st.markdown("### 🚀 **Quick Actions**")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🍎 Log Food", use_container_width=True, key="home_food_btn"):
            st.session_state.redirect_to = PAGE_DAILY_FOOD
            st.rerun()
    
    with col2:
        if st.button("📋 View Tasks", use_container_width=True, key="home_task_btn"):
            st.session_state.redirect_to = PAGE_TASK
            st.rerun()
    
    with col3:
        if st.button("🤖 Assistant", use_container_width=True, key="home_assistant_btn"):
            st.session_state.redirect_to = PAGE_ASSISTANT
            st.rerun()
    
    with col4:
        if st.button("👤 Edit Profile", use_container_width=True, key="home_profile_btn", 
                    help="Update your health profile - CVD risk automatically recalculates"):
            st.session_state.redirect_to = PAGE_PROFILE
            st.rerun()

elif st.session_state.current_page == PAGE_DAILY_FOOD:
    daily_food.main()

elif st.session_state.current_page == PAGE_TASK:
    tasks.main()

elif st.session_state.current_page == PAGE_ASSISTANT:
    assistant_voice.main()

elif st.session_state.current_page == PAGE_PROFILE:
    profile.main()
